import 'dart:async';

import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/books_service.dart';
import 'package:e_library/features/books/ui/components/books_list.dart';
import 'package:flutter/material.dart';

class SearchBooksList extends StatefulWidget {
  final String searchQuery;
  final VoidCallback? onEmptyResults;

  const SearchBooksList({
    super.key,
    required this.searchQuery,
    this.onEmptyResults,
  });

  @override
  State<SearchBooksList> createState() => _SearchBooksListState();
}

class _SearchBooksListState extends State<SearchBooksList> {
  String _currentQuery = '';
  Timer? _debounceTimer;
  BooksList? _booksList;

  @override
  void initState() {
    super.initState();
    _updateSearchQuery(widget.searchQuery);
  }

  @override
  void didUpdateWidget(SearchBooksList oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if search query changed
    if (widget.searchQuery != oldWidget.searchQuery) {
      _updateSearchQuery(widget.searchQuery);
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _updateSearchQuery(String newQuery) {
    // Cancel any existing debounce timer
    _debounceTimer?.cancel();

    // Update current query immediately for UI consistency
    _currentQuery = newQuery.trim();

    // Debounce the search for non-empty queries
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _rebuildBooksList();
    });
  }

  void _rebuildBooksList() {
    setState(() {
      _booksList = _buildBooksList();
    });
  }

  BooksList _buildBooksList() {
    return BooksList(
      key: ValueKey(_currentQuery), // Force rebuild when query changes
      booksFetcher: _currentQuery.isEmpty
          ? (page) async => PaginatedResponse.empty() // Return empty for no query
          : (page) async {
              final response = await BooksService.searchBooks(
                query: _currentQuery,
                page: page,
              );

              // Notify parent if no results found on first page
              if (page == 1 && response.data.isEmpty && widget.onEmptyResults != null) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  widget.onEmptyResults!();
                });
              }

              return response;
            },
      emptyStateTitle: 'No books found',
      emptyStateMessage: _currentQuery.isNotEmpty
          ? 'Try searching with different keywords'
          : 'Enter a search term to find books',
      errorTitle: 'Failed to search books',
    );
  }

  @override
  Widget build(BuildContext context) {
    return _booksList ?? _buildBooksList();
  }

}
